<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API修复测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>API修复测试</h1>
    
    <h2>测试吸音系数API</h2>
    <button onclick="testSoundAbsorptionAPI()">测试吸音系数零件API</button>
    <div id="absorption-result"></div>
    
    <h2>测试隔声量API</h2>
    <button onclick="testSoundTransmissionAPI()">测试隔声量零件API</button>
    <div id="transmission-result"></div>
    
    <h2>测试上墙法API</h2>
    <button onclick="testWallMountedAPI()">测试上墙法零件API</button>
    <div id="wall-result"></div>

    <script>
        async function testSoundAbsorptionAPI() {
            try {
                const response = await fetch('/sound_absorption/api/parts');
                const result = await response.json();
                
                console.log('吸音系数API响应:', result);
                
                if (result.code === 200) {
                    document.getElementById('absorption-result').innerHTML = 
                        `<p style="color: green;">✅ 成功！返回 ${result.data.length} 个零件</p>
                         <pre>${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    document.getElementById('absorption-result').innerHTML = 
                        `<p style="color: red;">❌ 失败：${result.message}</p>`;
                }
            } catch (error) {
                document.getElementById('absorption-result').innerHTML = 
                    `<p style="color: red;">❌ 错误：${error.message}</p>`;
            }
        }
        
        async function testSoundTransmissionAPI() {
            try {
                const response = await fetch('/sound_transmission/api/parts');
                const result = await response.json();
                
                console.log('隔声量API响应:', result);
                
                if (result.code === 200) {
                    document.getElementById('transmission-result').innerHTML = 
                        `<p style="color: green;">✅ 成功！返回 ${result.data.length} 个零件</p>
                         <pre>${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    document.getElementById('transmission-result').innerHTML = 
                        `<p style="color: red;">❌ 失败：${result.message}</p>`;
                }
            } catch (error) {
                document.getElementById('transmission-result').innerHTML = 
                    `<p style="color: red;">❌ 错误：${error.message}</p>`;
            }
        }
        
        async function testWallMountedAPI() {
            try {
                const response = await fetch('/wall_mounted_transmission/api/parts');
                const result = await response.json();
                
                console.log('上墙法API响应:', result);
                
                if (result.code === 200) {
                    document.getElementById('wall-result').innerHTML = 
                        `<p style="color: green;">✅ 成功！返回 ${result.data.length} 个零件</p>
                         <pre>${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    document.getElementById('wall-result').innerHTML = 
                        `<p style="color: red;">❌ 失败：${result.message}</p>`;
                }
            } catch (error) {
                document.getElementById('wall-result').innerHTML = 
                    `<p style="color: red;">❌ 错误：${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
