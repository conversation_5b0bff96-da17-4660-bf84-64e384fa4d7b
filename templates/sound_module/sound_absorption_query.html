{% extends "base.html" %}

{% block title %}垂直入射法吸音系数查询 - NVH数据管理系统{% endblock %}

{% block head %}
<link href="{{ url_for('static', filename='css/sound_insulation.css') }}" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script src="{{ url_for('static', filename='js/sound_absorption.js') }}"></script>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">垂直入射法吸音系数查询</h1>
</div>

<!-- 查询条件 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>查询条件
        </h5>
    </div>
    <div class="card-body">
        <form id="query-form">
            <div class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="part-select" class="form-label">零件</label>
                    <select class="form-select" id="part-select" required>
                        <option value="">请选择零件</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="material-select" class="form-label">材料</label>
                    <select class="form-select" id="material-select" required disabled>
                        <option value="">请先选择零件</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="weight-select" class="form-label">克重</label>
                    <select class="form-select" id="weight-select" required disabled>
                        <option value="">请先选择材料</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-primary" id="search-btn" disabled>
                        <i class="fas fa-search me-1"></i>查询
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" id="multi-compare-btn" disabled>
                        <i class="fas fa-chart-line me-1"></i>多克重对比
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 结果展示区域 -->
<div class="card" id="results-card" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>吸音系数查询结果
        </h5>
        <div class="d-flex align-items-center gap-3">
            <button type="button" class="btn btn-outline-info btn-sm" id="view-image-btn">
                <i class="fas fa-image me-1"></i>查看测试附图
            </button>
            <button type="button" class="btn btn-outline-success btn-sm" id="export-btn">
                <i class="fas fa-download me-1"></i>导出数据
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- 基础信息卡片 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-info-circle me-2"></i>基础信息</h6>
                        <div class="row" id="basic-info">
                            <!-- 动态生成基础信息 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="table-responsive mb-4">
            <table class="table table-striped table-hover" id="data-table">
                <thead class="table-dark">
                    <tr id="frequency-header">
                        <th>数据类型</th>
                        <!-- 动态生成频率列标题 -->
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态生成表格内容 -->
                </tbody>
            </table>
        </div>
        
        <!-- 折线对比图 -->
        <div class="mb-4">
            <h6><i class="fas fa-chart-line me-2"></i>吸音系数曲线图</h6>
            <div id="chart-container" style="width: 100%; height: 400px; min-height: 400px;"></div>
        </div>
    </div>
</div>

<!-- 多克重对比结果区域 -->
<div class="card" id="comparison-results-card" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-chart-bar me-2"></i>多克重对比结果
        </h5>
        <div class="d-flex align-items-center gap-3">
            <span id="comparison-count" class="badge bg-secondary">0 个克重</span>
            <button type="button" class="btn btn-outline-success btn-sm" id="export-comparison-btn">
                <i class="fas fa-download me-1"></i>导出对比数据
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- 对比信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-info-circle me-2"></i>对比信息</h6>
                        <div class="row" id="comparison-info">
                            <!-- 动态生成对比信息 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 对比数据表格 -->
        <div class="table-responsive mb-4">
            <table class="table table-striped table-hover table-sm" id="comparison-table">
                <thead class="table-dark">
                    <!-- 动态生成表头 -->
                </thead>
                <tbody>
                    <!-- 动态生成表格内容 -->
                </tbody>
            </table>
        </div>
        
        <!-- 对比折线图 -->
        <div class="mb-4">
            <h6><i class="fas fa-chart-line me-2"></i>多克重对比曲线图</h6>
            <div id="comparison-chart-container" style="width: 100%; height: 500px; min-height: 500px;"></div>
        </div>
    </div>
</div>

<!-- 空状态提示 -->
<div class="card" id="empty-state" style="display: block;">
    <div class="card-body text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">请选择查询条件</h5>
        <p class="text-muted">选择零件、材料和克重，点击"查询"按钮查看吸音系数数据</p>
    </div>
</div>

<!-- 加载提示 -->
<div class="text-center py-4" id="loading-indicator" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2 text-muted">正在查询数据...</p>
</div>

<!-- 多克重选择模态框 -->
<div class="modal fade" id="multiWeightModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择对比克重</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted mb-3">请选择要对比的克重（至少选择2个）：</p>
                <div id="weight-checkboxes">
                    <!-- 动态生成克重复选框 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirm-comparison-btn" disabled>确认对比</button>
            </div>
        </div>
    </div>
</div>

<!-- 测试图片模态框 -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">测试附图</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="image-info" class="mb-3">
                    <!-- 动态生成图片信息 -->
                </div>
                <div class="text-center">
                    <img id="test-image" src="" alt="测试图片" class="img-fluid" style="max-height: 500px;">
                    <div id="no-image" class="text-muted" style="display: none;">
                        <i class="fas fa-image fa-3x mb-3"></i>
                        <p>暂无测试图片</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}


