from flask import Blueprint, render_template, request, send_file
from decorators import login_required
from utils.result import success, error, bad_request
from services.wall_mounted_transmission_service import wall_mounted_transmission_service
import io

# 创建蓝图
wall_mounted_transmission_bp = Blueprint('wall_mounted_transmission', __name__, url_prefix='/wall_mounted_transmission')

class WallMountedTransmissionController:
    """上墙法隔声量控制器"""
    
    @staticmethod
    @wall_mounted_transmission_bp.route('/transmission_loss_query')
    @login_required
    def transmission_loss_query_page():
        """上墙法隔声量查询页面"""
        return render_template('wall_mounted_transmission/transmission_loss_query.html')
    
    @staticmethod
    @wall_mounted_transmission_bp.route('/api/parts')
    @login_required
    def get_parts():
        """获取零件列表"""
        try:
            parts = wall_mounted_transmission_service.get_part_list()
            return success(parts)
        except Exception as e:
            return error(f"获取零件列表失败: {str(e)}")
    
    @staticmethod
    @wall_mounted_transmission_bp.route('/api/materials')
    @login_required
    def get_materials():
        """获取材料列表"""
        try:
            part_name = request.args.get('part_name')
            materials = wall_mounted_transmission_service.get_material_list(part_name)
            return success(materials)
        except Exception as e:
            return error(f"获取材料列表失败: {str(e)}")
    
    @staticmethod
    @wall_mounted_transmission_bp.route('/api/weights')
    @login_required
    def get_weights():
        """获取克重列表"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            
            if not part_name or not material_name:
                return bad_request("请提供零件名称和材料名称")
            
            weights = wall_mounted_transmission_service.get_weight_list(part_name, material_name)
            return success(weights)
        except Exception as e:
            return error(f"获取克重列表失败: {str(e)}")
    
    @staticmethod
    @wall_mounted_transmission_bp.route('/api/transmission_data')
    @login_required
    def get_transmission_data():
        """获取上墙法隔声量数据"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')
            
            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")
            
            weight = float(weight)
            data = wall_mounted_transmission_service.get_transmission_data(part_name, material_name, weight)
            
            if not data:
                return error("未找到匹配的数据")
            
            return success(data, "数据获取成功")
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"获取上墙法隔声量数据失败: {str(e)}")
    
    @staticmethod
    @wall_mounted_transmission_bp.route('/api/export_csv')
    @login_required
    def export_csv():
        """导出CSV数据"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')
            
            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")
            
            weight = float(weight)
            csv_content = wall_mounted_transmission_service.export_data_to_csv(part_name, material_name, weight)
            
            if not csv_content:
                return error("未找到匹配的数据")
            
            # 创建响应
            output = io.BytesIO()
            output.write(csv_content.encode('utf-8-sig'))  # 使用UTF-8 BOM以支持Excel
            output.seek(0)
            
            filename = f"上墙法隔声量_{part_name}_{material_name}_{int(weight)}g_m2.csv"
            
            return send_file(
                output,
                mimetype='text/csv',
                as_attachment=True,
                download_name=filename
            )
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"导出数据失败: {str(e)}")
    
    @staticmethod
    @wall_mounted_transmission_bp.route('/api/test_image')
    @login_required
    def get_test_image():
        """获取测试图片信息"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')
            
            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")
            
            weight = float(weight)
            image_info = wall_mounted_transmission_service.get_test_image_info(part_name, material_name, weight)
            
            if not image_info:
                return error("未找到匹配的数据")
            
            return success(image_info)
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"获取测试图片信息失败: {str(e)}")
    
    @staticmethod
    @wall_mounted_transmission_bp.route('/api/manufacturers')
    @login_required
    def get_manufacturers():
        """获取厂家列表"""
        try:
            manufacturers = wall_mounted_transmission_service.get_manufacturer_list()
            return success(manufacturers)
        except Exception as e:
            return error(f"获取厂家列表失败: {str(e)}")
    
    @staticmethod
    @wall_mounted_transmission_bp.route('/api/search')
    @login_required
    def search_data():
        """搜索数据"""
        try:
            filters = {}
            if request.args.get('part_name'):
                filters['part_name'] = request.args.get('part_name')
            if request.args.get('material_name'):
                filters['material_name'] = request.args.get('material_name')
            if request.args.get('weight_min'):
                filters['weight_min'] = float(request.args.get('weight_min'))
            if request.args.get('weight_max'):
                filters['weight_max'] = float(request.args.get('weight_max'))
            
            data = wall_mounted_transmission_service.search_data(filters if filters else None)
            return success(data)
        except Exception as e:
            return error(f"搜索数据失败: {str(e)}")
    
    @staticmethod
    @wall_mounted_transmission_bp.route('/api/statistics')
    @login_required
    def get_statistics():
        """获取统计信息"""
        try:
            stats = wall_mounted_transmission_service.get_statistics()
            return success(stats)
        except Exception as e:
            return error(f"获取统计信息失败: {str(e)}")

# 注册蓝图到应用
def register_wall_mounted_transmission_routes(app):
    """注册上墙法隔声量路由"""
    app.register_blueprint(wall_mounted_transmission_bp)
